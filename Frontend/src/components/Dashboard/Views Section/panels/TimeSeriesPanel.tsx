import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../types';
import { PanelFilter } from '../FilterTypes';
import { Spin, Empty, Tabs, Switch, Tooltip } from 'antd';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useSelector, useDispatch } from "react-redux";
import { addAnnotation, setActiveColumnName, setAnnotations } from '../../../../Redux/slices/annotationSlice';
import { addOperation, setActiveColumnName as setOperationActiveColumnName ,setOperations} from '../../../../Redux/slices/operationSlice';
import { postRequest } from '../../../../utils/apiHandler';
interface TimeSeriesPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string,data?:any) => void;
}

const TimeSeriesPanel: React.FC<TimeSeriesPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  onZoomSelection
}) => {

  console.log('original data in (Time Series Component)', data);
  console.log('data after filteredData in (Time Series Component)', filteredData);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);
  const [plotHeight, setPlotHeight] = useState(300);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [activeKey, setActiveKey] = useState("0");
  const [drawMode, setDrawMode] = useState<string>('select'); // Add state for tracking draw mode
  const [showFullData, setShowFullData] = useState<boolean>(false); // Toggle between filtered and full data view

  // Selection popup state
  const [selectionPopup, setSelectionPopup] = useState<{
    visible: boolean;
    x: number;
    y: number;
    selectionData: any;
  }>({
    visible: false,
    x: 0,
    y: 0,
    selectionData: null
  });
  const annotations = useSelector((state: any) => state.annotations);
  const selectedAnnotations = useSelector((state: any) => state.annotations.annotations);
  const operationsState = useSelector((state: any) => state.operations);
  const dispatch = useDispatch();
  const selectSystems = useSelector((state: any) => state.systems.systems);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const updateSize = () => {
      const rect = container.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      if (width > 0 && height > 0) {
        setContainerSize({ width, height });
        setPlotHeight(Math.floor(height * 0.8));
        setLayout((prev: any) => ({
          ...prev,
          height: Math.floor(height * 0.8)
        }));
        // Set chart ready after a small delay to ensure DOM is stable
        setTimeout(() => setIsChartReady(true), 100);
      }
    };

    // Initial size update
    updateSize();

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setContainerSize({ width, height });
          setPlotHeight(Math.floor(height * 0.8));
          setLayout((prev: any) => ({
            ...prev,
            height: Math.floor(height * 0.8)
          }));
          // Set chart ready after a small delay to ensure DOM is stable
          setTimeout(() => setIsChartReady(true), 100);
        }
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, []);


  // pass the intial default selected column name
  useEffect(()=>{
    if (columnTabs.length > 0) {
      dispatch(setActiveColumnName(columnTabs[parseInt(activeKey)].name))
      dispatch(setOperationActiveColumnName(columnTabs[parseInt(activeKey)].name))
    }
  },[])

  const fetchAnnotations = async () => {
    try {
      const dataToSend = {
        columnName: columnTabs[parseInt(activeKey)].name,
        systems: JSON.stringify(selectSystems[0]?.systems || []),
      };

      const response = await postRequest('/file/get-json-file', dataToSend);

      if (response?.data?.data) {
        const { anomaly, operation } = response.data.data;

        dispatch(
          setAnnotations([
            {
              columnName: columnTabs[parseInt(activeKey)].name,
              annotations: anomaly,
            },
          ])
        );
        dispatch(
          setOperations([
            {
              columnName: columnTabs[parseInt(activeKey)].name,
              operations: operation,
            },
          ])
        )
      }
    } catch (error) {
      console.error('Error fetching annotations:', error);
    }
  };

  useEffect(() => {
    if (columnTabs.length > 0 && columnTabs[parseInt(activeKey)]) {
      fetchAnnotations();
    }
  }, [activeKey]);

  const setZoomLayout = () => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      console.log('ZOOM LAYOUT - No valid column tabs or activeKey');
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];
    console.log('ZOOM LAYOUT - Selected Tab:', selectedTab.name);

    // Log the data we're working with
    console.log('ZOOM LAYOUT - Data available:', {
      filteredDataAvailable: filteredData && filteredData.length > 0,
      filteredDataLength: filteredData ? filteredData.length : 0,
      originalDataLength: data ? data.length : 0
    });

    // First, get extremes from filtered data (if available)
    const filteredExtremes = filteredData && filteredData.length > 0 ?
      getExtremes(filteredData, selectedTab.name) : null;

    // Also get extremes from original data as a fallback
    const originalExtremes = getExtremes(data, selectedTab.name);

    // Use filtered extremes if available, otherwise use original
    const extremes = filteredExtremes || originalExtremes;

    console.log('ZOOM LAYOUT - Extremes calculation:', {
      filteredExtremes: filteredExtremes,
      originalExtremes: originalExtremes,
      usingFilteredData: !!filteredExtremes
    });

    if (extremes) {
      console.log('ZOOM LAYOUT - Setting zoom layout based on data extremes:', extremes);

      // For x-axis, use date filter if available, otherwise use data extremes
      const xRange = dateFilter.startDate && dateFilter.endDate ?
        [dateFilter.startDate, dateFilter.endDate] :
        [extremes.DateTime.min, extremes.DateTime.max];

      // Calculate y-axis range with proper padding
      const yMin = extremes[selectedTab.name].min as any;
      const yMax = extremes[selectedTab.name].max as any;

      console.log('ZOOM LAYOUT - Y-axis range calculation:', {
        min: yMin,
        max: yMax,
        range: yMax - yMin
      });

      setLayout((prev: any) => {
        const newLayout = {
          ...prev,
          xaxis: {
            ...(prev?.xaxis || {}),
            range: xRange,
            fixedrange: false, // Allow x-axis zooming
          },
          yaxis: {
            ...(prev?.yaxis || {}),
            // Set the range but keep it fixed
            range: [yMin, yMax],
            fixedrange: true, // Lock y-axis when zooming
            autorange: false, // Disable autorange to use our custom range
          },
          dragmode: drawMode,
        };

        console.log('ZOOM LAYOUT - New layout being set:', {
          xaxisRange: newLayout.xaxis.range,
          yaxisRange: newLayout.yaxis.range,
          yaxisAutorange: newLayout.yaxis.autorange
        });

        return newLayout;
      });
    } else {
      console.log('ZOOM LAYOUT - No extremes available, cannot set layout');
    }
  }


  const getExtremes = (data: any[], key: string) => {
    console.log('GET EXTREMES - Starting calculation for key:', key);
    console.log('GET EXTREMES - Data sample:', data.slice(0, 3));

    if (!Array.isArray(data) || data.length === 0 || !key) {
      console.log('GET EXTREMES - Invalid data or key:', {
        isArray: Array.isArray(data),
        length: data?.length,
        key: key
      });
      return null;
    }

    let minKeyVal = Infinity;
    let maxKeyVal = -Infinity;
    let minDate = new Date(data[0].DateTime);
    let maxDate = new Date(data[0].DateTime);

    // Count valid values for debugging
    let validValueCount = 0;
    let nanValueCount = 0;

    for (let item of data) {
      const keyVal = parseFloat(item[key]);
      const date = new Date(item.DateTime);

      if (!isNaN(keyVal)) {
        minKeyVal = Math.min(minKeyVal, keyVal);
        maxKeyVal = Math.max(maxKeyVal, keyVal);
        validValueCount++;
      } else {
        nanValueCount++;
      }

      if (!isNaN(date.getTime())) {
        if (date < minDate) minDate = date;
        if (date > maxDate) maxDate = date;
      }
    }

    console.log('GET EXTREMES - Value counts:', {
      validValues: validValueCount,
      nanValues: nanValueCount,
      totalRows: data.length
    });

    console.log('GET EXTREMES - Raw extremes:', {
      minKeyVal: minKeyVal,
      maxKeyVal: maxKeyVal,
      minDate: minDate,
      maxDate: maxDate
    });

    // Add padding to the y-axis range to avoid stretched appearance
    // Calculate a padding of 20% of the range on both sides (increased from 10%)
    const range = maxKeyVal - minKeyVal;

    // Special case for when min and max are the same (flat line)
    if (range === 0 || range < 0.000001) {
      console.log('GET EXTREMES - Flat line detected, using special padding');
      // Use 50% of the value as padding, with a minimum of 1
      const flatLinePadding = Math.max(1, Math.abs(maxKeyVal) * 0.5);

      // Apply padding to min and max values
      const paddedMin = minKeyVal - flatLinePadding;
      const paddedMax = maxKeyVal + flatLinePadding;

      console.log('GET EXTREMES - Flat line padding:', {
        value: maxKeyVal,
        padding: flatLinePadding,
        paddedMin: paddedMin,
        paddedMax: paddedMax
      });

      const result = {
        [key]: {
          min: paddedMin,
          max: paddedMax,
          rawMin: minKeyVal,
          rawMax: maxKeyVal
        },
        DateTime: {
          min: minDate.toISOString(),
          max: maxDate.toISOString()
        }
      };

      console.log('GET EXTREMES - Final result for flat line:', result);
      return result;
    }

    const padding = range * 0.2;

    // If range is very small (near zero), use a fixed padding
    const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

    // Apply padding to min and max values
    const paddedMin = minKeyVal - effectivePadding;
    const paddedMax = maxKeyVal + effectivePadding;

    console.log('GET EXTREMES - Padding calculation:', {
      range: range,
      padding: padding,
      effectivePadding: effectivePadding,
      paddedMin: paddedMin,
      paddedMax: paddedMax
    });

    const result = {
      [key]: {
        min: paddedMin,
        max: paddedMax,
        rawMin: minKeyVal,
        rawMax: maxKeyVal
      },
      DateTime: {
        min: minDate.toISOString(),
        max: maxDate.toISOString()
      }
    };

    console.log('GET EXTREMES - Final result:', result);
    return result;
  };

  // ECharts helper functions
  const createEChartsOption = (seriesData: any[], title: string, yRange?: [number, number]) => {
    if (!seriesData || seriesData.length === 0) return {};

    // Get annotations and operations for current column
    const currentColumnName = title;
    const columnAnnotations = selectedAnnotations.find((ann: any) => ann.columnName === currentColumnName)?.annotations?.filter((a: any) => a.selected === true && a.applyAsFilter !== true) || [];
    const columnOperations = operationsState?.operations?.find((op: any) => op.columnName === currentColumnName)?.operations?.filter((o: any) => o.selected === true) || [];

    // Debug logging
    console.log(`ECharts - Column: ${currentColumnName}`);
    console.log('ECharts - Annotations:', columnAnnotations);
    console.log('ECharts - Operations:', columnOperations);
    console.log('ECharts - Selected Annotations State:', selectedAnnotations);
    console.log('ECharts - Operations State:', operationsState);

    // Create markArea for annotations (red rectangles)
    const markAreas = columnAnnotations.map((annotation: any) => [
      {
        xAxis: new Date(annotation.x0).getTime(),
        yAxis: annotation.y0
      },
      {
        xAxis: new Date(annotation.x1).getTime(),
        yAxis: annotation.y1
      }
    ]);

    // Create markLine for operations (green horizontal lines)
    const markLines: any[] = [];
    columnOperations.forEach((operation: any, opIndex: number) => {
      // Add line for y0 (lower bound) - no label
      markLines.push({
        yAxis: operation.y0,
        lineStyle: {
          color: 'rgba(0, 128, 0, 0.7)',
          width: 2,
          type: 'dashed'
        },
        label: {
          show: false
        }
      });

      // Add line for y1 (upper bound) - with operation name label on left side
      markLines.push({
        yAxis: operation.y1,
        lineStyle: {
          color: 'rgba(0, 128, 0, 0.7)',
          width: 2,
          type: 'dashed'
        },
        label: {
          show: true,
          position: 'start',
          formatter: operation?.label?.text || `Operation ${opIndex + 1}`,
          color: 'rgba(0, 128, 0, 0.8)',
          fontSize: 12,
          fontWeight: 'bold',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderColor: 'rgba(0, 128, 0, 0.7)',
          borderWidth: 1,
          borderRadius: 3,
          padding: [2, 4]
        }
      });
    });

    console.log('ECharts - Created markAreas:', markAreas);
    console.log('ECharts - Created markLines:', markLines);

    const series = seriesData.map((s, index) => ({
      name: s.name,
      type: 'line',
      data: s.x?.map((x: any, i: number) => [x, s.y[i]]) || [],
      smooth: false,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        width: 2,
      },
      emphasis: {
        focus: 'series'
      },
      // Add markArea for annotations (only for the first series to avoid duplicates)
      markArea: index === 0 && markAreas.length > 0 ? {
        silent: true,
        itemStyle: {
          color: 'rgba(255, 0, 0, 0.1)',
          borderColor: 'rgba(255, 0, 0, 0.7)',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'inside',
          formatter: function(params: any) {
            const annotation = columnAnnotations[params.dataIndex];
            return annotation?.label?.text || 'Anomaly';
          },
          color: 'rgba(255, 0, 0, 0.8)',
          fontSize: 12,
          fontWeight: 'bold'
        },
        data: markAreas
      } : undefined,
      // Add markLine for operations (only for the first series to avoid duplicates)
      markLine: index === 0 && markLines.length > 0 ? {
        silent: true,
        data: markLines
      } : undefined
    }));

    return {
      animation: false, // Disable all animations for better performance
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          let result = `${new Date(params[0].value[0]).toLocaleString()}<br/>`;
          params.forEach((param: any) => {
            result += `${param.seriesName}: ${param.value[1]}<br/>`;
          });
          return result;
        }
      },
      legend: {
        data: series.map(s => s.name),
        top: 30,
        type: 'scroll'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          brush: {
            type: ['rect', 'polygon', 'clear']
          },
          saveAsImage: {}
        }
      },
      xAxis: {
        type: 'time',
        boundaryGap: false,
        axisLine: {
          onZero: false
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        min: yRange ? yRange[0] : 'dataMin',
        max: yRange ? yRange[1] : 'dataMax',
        scale: true,
        splitArea: {
          show: true
        }
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: [0],
          filterMode: 'filter'
        }
      ],
      brush: {
        toolbox: ['rect', 'polygon', 'clear'],
        xAxisIndex: 0
      },
      series: series
    };
  };

  const createStackedEChartsOption = (seriesData: any[]) => {
    if (!seriesData || seriesData.length === 0) return {};

    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ];

    // For stacked charts, we'll add annotations and operations to individual series
    // This approach works better with multiple subplots

    return {
      animation: false, // Disable all animations for better performance
      title: {
        text: `Time Series (${seriesData.length} columns)`,
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: seriesData.map(s => s.name),
        top: 30,
        type: 'scroll'
      },
      grid: seriesData.map((_, index) => ({
        left: '3%',
        right: '4%',
        top: `${15 + index * (80 / seriesData.length)}%`,
        height: `${75 / seriesData.length}%`,
        containLabel: true
      })),
      xAxis: seriesData.map((_, index) => ({
        type: 'time',
        gridIndex: index,
        boundaryGap: false,
        axisLine: {
          onZero: false
        },
        show: index === seriesData.length - 1 // Only show x-axis on bottom chart
      })),
      yAxis: seriesData.map((s, index) => ({
        type: 'value',
        gridIndex: index,
        name: s.name,
        nameLocation: 'middle',
        nameGap: 50,
        scale: true,
        splitArea: {
          show: true
        }
      })),
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: seriesData.map((_, i) => i),
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: seriesData.map((_, i) => i),
          filterMode: 'filter'
        }
      ],
      series: seriesData.map((s, index) => {
        // Get annotations and operations for this specific series
        const columnAnnotations = selectedAnnotations.find((ann: any) => ann.columnName === s.name)?.annotations?.filter((a: any) => a.selected === true && a.applyAsFilter !== true) || [];
        const columnOperations = operationsState?.operations?.find((op: any) => op.columnName === s.name)?.operations?.filter((o: any) => o.selected === true) || [];

        // Debug logging for stacked charts
        console.log(`ECharts Stacked - Series: ${s.name}`);
        console.log('ECharts Stacked - Annotations:', columnAnnotations);
        console.log('ECharts Stacked - Operations:', columnOperations);

        // Create markArea for annotations
        const markAreas = columnAnnotations.map((annotation: any) => [
          {
            xAxis: new Date(annotation.x0).getTime(),
            yAxis: annotation.y0
          },
          {
            xAxis: new Date(annotation.x1).getTime(),
            yAxis: annotation.y1
          }
        ]);

        // Create markLine for operations
        const markLines: any[] = [];
        columnOperations.forEach((operation: any, opIndex: number) => {
          // Lower bound - no label
          markLines.push({
            yAxis: operation.y0,
            lineStyle: {
              color: 'rgba(0, 128, 0, 0.7)',
              width: 2,
              type: 'dashed'
            },
            label: {
              show: false
            }
          });
          // Upper bound - with operation name label
          markLines.push({
            yAxis: operation.y1,
            lineStyle: {
              color: 'rgba(0, 128, 0, 0.7)',
              width: 2,
              type: 'dashed'
            },
            label: {
              show: true,
              position: 'start',
              formatter: operation?.label?.text || `Operation ${opIndex + 1}`,
              color: 'rgba(0, 128, 0, 0.8)',
              fontSize: 12,
              fontWeight: 'bold',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              borderColor: 'rgba(0, 128, 0, 0.7)',
              borderWidth: 1,
              borderRadius: 3,
              padding: [2, 4]
            }
          });
        });

        return {
          name: s.name,
          type: 'line',
          xAxisIndex: index,
          yAxisIndex: index,
          data: s.x?.map((x: any, i: number) => [x, s.y[i]]) || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            focus: 'series'
          },
          // Add markArea for annotations
          markArea: markAreas.length > 0 ? {
            silent: true,
            itemStyle: {
              color: 'rgba(255, 0, 0, 0.1)',
              borderColor: 'rgba(255, 0, 0, 0.7)',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'inside',
              formatter: function(params: any) {
                const annotation = columnAnnotations[params.dataIndex];
                return annotation?.label?.text || 'Anomaly';
              },
              color: 'rgba(255, 0, 0, 0.8)',
              fontSize: 12,
              fontWeight: 'bold'
            },
            data: markAreas
          } : undefined,
          // Add markLine for operations
          markLine: markLines.length > 0 ? {
            silent: true,
            data: markLines
          } : undefined
        };
      })
    };
  };

  // ECharts event handlers
  const handleEChartsEvents = {
    brushEnd: (params: any) => {
      // Only trigger popup when brush selection is complete
      if (params.areas && params.areas.length > 0) {
        const area = params.areas[0];
        const coordRange = area.coordRange;

        if (coordRange && coordRange.length >= 2) {
          const [xRange, yRange] = coordRange;
          const selectedTab = columnTabs[parseInt(activeKey)]?.name;

          const selectionData = {
            x1: xRange[0],
            x2: xRange[1],
            y1: yRange[0],
            y2: yRange[1],
            selectedTab: selectedTab,
            xRange,
            yRange
          };

          // Show popup at center of the panel after selection is complete
          setTimeout(() => {
            setSelectionPopup({
              visible: true,
              x: 0,
              y: 0,
              selectionData
            });
          }, 100); // Small delay to ensure selection is complete
        }
      }
    },

    dataZoom: (params: any) => {
      if (params.batch && params.batch.length > 0) {
        const zoomInfo = params.batch[0];
        if (zoomInfo.startValue !== undefined && zoomInfo.endValue !== undefined) {
          const selectedTab = columnTabs[parseInt(activeKey)]?.name;
          const data = {
            x1: zoomInfo.startValue,
            x2: zoomInfo.endValue,
            selectedTab
          };

          // Call parent's zoom handler
          onZoomSelection?.('date', zoomInfo.startValue, zoomInfo.endValue, 'time-series-panel', data);
        }
      }
    }
  };

  // Get annotations from Redux store - used in other parts of the component
  useSelector((state: any) => state.annotations);

  // Process data for the time series chart
  const plotData = useMemo(() => {
    console.log('TimeSeriesPanel - useMemo triggered');
    console.log('TimeSeriesPanel - panelFilters:', panelFilters);

    // Use filtered data or original data based on toggle state
    // Note: We'll still use the data for rendering even when it's filtered out by annotations
    const dataToProcess = showFullData ? data : (filteredData || data);

    console.log('TimeSeriesPanel - Data to process:', {
      usingFilteredData: showFullData ? false : !!filteredData,
      dataLength: dataToProcess?.length || 0
    });

    if (!dataToProcess || isLoading) return [];

    try {
      // Check if we have time series data
      if (!dataToProcess.timeSeries || !Array.isArray(dataToProcess.timeSeries)) {
        // Try to extract time series data from tabular data
        if (Array.isArray(dataToProcess) && dataToProcess.length > 0) {
          // Assuming first column is date/time and other columns are values
          const headers = Array.isArray(dataToProcess[0]) ? dataToProcess[0] : Object.keys(dataToProcess[0]);
          const rows = Array.isArray(dataToProcess[0]) ? dataToProcess.slice(1) : dataToProcess;

          // Find datetime column - exact match only (case insensitive)
          const dateColumnIndex = headers.findIndex(h =>
            h === 'DateTime'
          );

          if (dateColumnIndex >= 0) {
            // Create a series for each numeric column
            const series = [];

            for (let i = 0; i < headers.length; i++) {
              if (i !== dateColumnIndex) {
                const columnData = rows.map(row => {
                  const value = Array.isArray(row) ? row[i] : row[headers[i]];
                  return parseFloat(value);
                }).filter(val => !isNaN(val));

                if (columnData.length > 0) {
                  series.push({
                    name: headers[i],
                    data: columnData,
                    x: rows.map(row => Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]]),
                    y: columnData,
                    type: 'scatter',
                    mode: 'lines+markers',
                  });
                }
              }
            }

            return series;
          }
        }
        return [];
      }

      // If we have structured time series data
      if (dataToProcess.timeSeries.categories && dataToProcess.timeSeries.series) {
        return dataToProcess.timeSeries.series.map((series: any) => ({
          name: series.name,
          x: dataToProcess.timeSeries.categories,
          y: series.data,
          type: 'scatter',
          mode: 'lines+markers',
        }));
      }

      return [];
    } catch (error) {
      console.error('Error processing time series data:', error);
      return [];
    }
  }, [data, filteredData, isLoading, panelFilters, showFullData]);

  // Apply only column selection, not date filtering
  const filteredPlotData = useMemo(() => {
    let filtered = [...plotData];


    // Apply column selection if any
    if (selectedColumns.indices.length > 0 && selectedColumns.headers.length > 0) {
      // Filter series by selected column headers
      filtered = filtered.filter(series =>
        selectedColumns.headers.includes(series.name)
      );
    }

    return filtered;
  }, [plotData, selectedColumns]);

  // Group data by column for tabs - defined early to avoid circular dependencies
  const columnTabs = useMemo(() => {
    // If no plot data, return empty array
    if (!filteredPlotData || filteredPlotData.length === 0) {
      console.log('columnTabs - No filtered plot data available');
      return [];
    }

    console.log('columnTabs - Creating tabs for columns:',
      filteredPlotData.map(series => series.name));

    // Create a tab for each column
    return filteredPlotData.map(series => {
      // Calculate y-axis range for this series
      if (series.y && series.y.length > 0) {
        const validYValues = series.y.filter((val: any) => !isNaN(parseFloat(val)));
        if (validYValues.length > 0) {
          const minY = Math.min(...validYValues.map((val: any) => parseFloat(val)));
          const maxY = Math.max(...validYValues.map((val: any) => parseFloat(val)));
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log(`columnTabs - Flat line detected for ${series.name}, using padding:`, flatLinePadding);

            return {
              name: series.name,
              data: [series],
              yRange: [minY - flatLinePadding, maxY + flatLinePadding]
            };
          }

          // Normal case with range
          const padding = range * 0.2;
          const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

          console.log(`columnTabs - Y-axis range for ${series.name}:`, {
            min: minY,
            max: maxY,
            range: range,
            padding: effectivePadding,
            paddedMin: minY - effectivePadding,
            paddedMax: maxY + effectivePadding
          });

          return {
            name: series.name,
            data: [series],
            yRange: [minY - effectivePadding, maxY + effectivePadding]
          };
        }
      }

      // Fallback if no valid y values
      return {
        name: series.name,
        data: [series]
      };
    });
  }, [filteredPlotData]);

  // Set initial zoom range based on dateFilter and update when data changes
  useEffect(() => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];

    // Get extremes from filtered data if available
    const dataToUse = filteredData && filteredData.length > 0 ? filteredData : data;
    const extremes = getExtremes(dataToUse, selectedTab.name);

    if (!extremes) return;

    // If dateFilter is set, use it for x-axis range
    const xRange = dateFilter.startDate && dateFilter.endDate ?
      [dateFilter.startDate, dateFilter.endDate] :
      [extremes.DateTime.min, extremes.DateTime.max];

    console.log('Setting zoom range based on data and filters:', {
      xRange,
      yRange: [extremes[selectedTab.name].min, extremes[selectedTab.name].max]
    });

    setLayout((prev: any) => ({
      ...prev,
      xaxis: {
        ...(prev?.xaxis || {}),
        range: xRange,
        fixedrange: false, // Allow x-axis zooming
      },
      yaxis: {
        ...(prev?.yaxis || {}),
        // Set the range with our calculated values (with padding)
        range: [
          extremes[selectedTab.name].min,
          extremes[selectedTab.name].max
        ],
        fixedrange: true, // Lock y-axis when zooming
        autorange: false, // Disable autorange to use our custom range
      },
      dragmode: drawMode, // Ensure the current draw mode is maintained
    }));
  }, [filteredData, data, dateFilter, drawMode, activeKey, columnTabs]);

  // We'll remove the toggle functionality as requested

  // ECharts layout state (keeping for compatibility with existing useEffect hooks)
  const [layout1, setLayout] = useState<any>({});




  // Popup action handlers
  const handlePopupAction = (action: string) => {
    const { selectionData } = selectionPopup;
    if (!selectionData) return;

    const { x1, x2, y1, y2, selectedTab } = selectionData;

    switch (action) {
      case 'zoom':
        // Call parent's zoom handler for ECharts
        if (onZoomSelection) {
          onZoomSelection('date', x1, x2, 'time-series-panel', selectionData);
        }
        break;

      case 'anomaly':
        // Create annotation for anomaly (maintaining compatibility with existing format)
        const anomalyAnnotation = {
          type: 'rect',
          xref: 'x',
          yref: 'y',
          x0: x1,
          y0: y1,
          x1: x2,
          y1: y2,
          fillcolor: 'rgba(255, 0, 0, 0.1)',
          line: {
            color: 'rgba(255, 0, 0, 0.7)',
            width: 2,
            dash: 'solid'
          },
          shapeId: `anomaly-${Date.now()}`,
          label: {
            text: 'Anomaly'
          },
          selected: true,
          applyAsFilter: false
        };

        dispatch(addAnnotation({
          columnName: selectedTab,
          annotation: anomalyAnnotation
        }));
        break;

      case 'operation':
        // Create operation range (maintaining compatibility with existing format)
        const operationRange = {
          type: 'line',
          xref: 'paper',
          yref: 'y',
          y0: y1,
          y1: y2,
          line: {
            color: 'rgba(0, 128, 0, 0.7)',
            width: 2,
            dash: 'dash'
          },
          layer: 'below',
          operationId: `operation-${Date.now()}`,
          label: {
            text: `Operation Range ${y1.toFixed(2)} - ${y2.toFixed(2)}`
          },
          selected: true
        };

        dispatch(addOperation({
          columnName: selectedTab,
          operation: operationRange
        }));
        break;
    }

    // Hide popup after action
    setSelectionPopup(prev => ({ ...prev, visible: false }));
  };

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectionPopup.visible) {
        const target = event.target as Element;
        if (!target.closest('.selection-popup')) {
          setSelectionPopup(prev => ({ ...prev, visible: false }));
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [selectionPopup.visible]);




  // ECharts chart update trigger - force re-render when annotations/operations change
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);

  useEffect(() => {
    // Trigger chart re-render when annotations or operations change
    setChartUpdateTrigger(prev => prev + 1);
  }, [selectedAnnotations, operationsState, activeKey]);


  // We don't need activeTab anymore as we're using columnTabs[parseInt(activeKey)] directly

  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // Ensure we update the active column name in both slices
    if (columnTabs.length > 0 && columnTabs[parseInt(key)]) {
      const tab = columnTabs[parseInt(key)];
      const columnName = tab.name;
      dispatch(setActiveColumnName(columnName));
      dispatch(setOperationActiveColumnName(columnName));

      // Fetch annotations for the new tab
      fetchAnnotations();

      // Log detailed information about the data and ranges for debugging
      console.log('TAB CHANGE - Column Name:', columnName);
      console.log('TAB CHANGE - Tab Data:', tab);

      // Log filtered data for this column
      const filteredDataForColumn = filteredData && filteredData.length > 0 ?
        filteredData.map((row: any) => ({
          DateTime: row.DateTime,
          [columnName]: row[columnName]
        })) : [];

      console.log('TAB CHANGE - Filtered Data Sample (first 5 rows):',
        filteredDataForColumn.slice(0, 5));
      console.log('TAB CHANGE - Filtered Data Length:',
        filteredDataForColumn.length);

      // Calculate and log min/max values for this column
      if (filteredDataForColumn.length > 0) {
        const yValues = filteredDataForColumn
          .map((row: any) => parseFloat(row[columnName]))
          .filter((val: number) => !isNaN(val));

        if (yValues.length > 0) {
          const minY = Math.min(...yValues);
          const maxY = Math.max(...yValues);
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log('TAB CHANGE - Flat line detected, using special padding:', flatLinePadding);

            // Update layout with flat line padding
            setLayout((prev: any) => ({
              ...prev,
              yaxis: {
                ...prev.yaxis,
                range: [minY - flatLinePadding, maxY + flatLinePadding],
                autorange: false
              }
            }));

            return; // Skip the rest of the function
          }

          const padding = range * 0.2;
          const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;
          const paddedMin = minY - effectivePadding;
          const paddedMax = maxY + effectivePadding;

          console.log('TAB CHANGE - Y-Axis Values:', {
            rawMin: minY,
            rawMax: maxY,
            range: range,
            padding: effectivePadding,
            paddedMin: paddedMin,
            paddedMax: paddedMax
          });

          // Directly update the layout with the calculated range
          setLayout((prev: any) => ({
            ...prev,
            yaxis: {
              ...prev.yaxis,
              range: [paddedMin, paddedMax],
              autorange: false
            }
          }));

          // Log current layout settings after update
          setTimeout(() => {
            console.log('TAB CHANGE - Updated Layout:', {
              xaxisRange: layout1.xaxis?.range,
              yaxisRange: layout1.yaxis?.range,
              yaxisAutorange: layout1.yaxis?.autorange
            });
          }, 100);

          return; // Skip the rest of the function
        }
      }

      // If we have a pre-calculated yRange in the tab, use it
      if (tab.yRange) {
        console.log('TAB CHANGE - Using pre-calculated yRange:', tab.yRange);

        setLayout((prev: any) => ({
          ...prev,
          yaxis: {
            ...prev.yaxis,
            range: tab.yRange,
            autorange: false
          }
        }));

        return; // Skip the rest of the function
      }

      // Log current layout settings
      console.log('TAB CHANGE - Current Layout:', {
        xaxisRange: layout1.xaxis?.range,
        yaxisRange: layout1.yaxis?.range,
        yaxisAutorange: layout1.yaxis?.autorange
      });
    }

    // Use setTimeout to ensure the tab change is processed before updating the layout
    // Only run this if we didn't already set the layout above
    setTimeout(() => {
      setZoomLayout();

      // Log the layout after update
      setTimeout(() => {
        console.log('TAB CHANGE - Updated Layout (from setZoomLayout):', {
          xaxisRange: layout1.xaxis?.range,
          yaxisRange: layout1.yaxis?.range,
          yaxisAutorange: layout1.yaxis?.autorange
        });
      }, 100);
    }, 0);
  };

  // Early returns after all hooks are called
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading time series data..." />
      </div>
    );
  }

  if (!filteredPlotData || filteredPlotData.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No time series data available" />
      </div>
    );
  }

  // Determine if we should show stacked view - always show stacked view when columns are selected
  const shouldShowStackedView = selectedColumns.indices.length > 0 && filteredPlotData.length > 0;

  // Render the component

    return (
      <div className="time-series-panel h-[95%]" ref={containerRef} style={{ position: 'relative' }}>
        {
          shouldShowStackedView ? (
          <>
          <div className="flex justify-between items-center mb-1 px-3 pt-1">
            <h3 className="text-base font-medium">Time Series ({filteredPlotData.length})</h3>
            <div className="flex items-center">
              <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                <Switch
                  checked={showFullData}
                  onChange={setShowFullData}
                  checkedChildren="Full Data"
                  unCheckedChildren="Filtered"
                  className="mr-2"
                  size="small"
                />
              </Tooltip>
            </div>
          </div>
        <div className="p-2 h-[calc(100%-30px)] overflow-auto">
          <div style={{ minHeight: `${Math.max(400, filteredPlotData.length * 150)}px` }}>
            {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
              <ReactECharts
                key={`stacked-${chartUpdateTrigger}`}
                ref={echartsRef}
                option={createStackedEChartsOption(filteredPlotData)}
                style={{ width: '100%', height: '100%', minHeight: '400px' }}
                onEvents={{
                  brushEnd: handleEChartsEvents.brushEnd,
                  dataZoom: handleEChartsEvents.dataZoom
                }}
                opts={{ renderer: 'canvas', animation: false }}
              />
            ) : (
              <div style={{ width: '100%', height: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Spin size="large" tip="Loading chart..." />
              </div>
            )}
          </div>
        </div></>) :(<>
          <div className="flex justify-between items-center mb-1 px-3 pt-0">
            <h3 className="text-base font-medium">Time Series</h3>
            <div className="flex items-center">
              <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                <Switch
                  checked={showFullData}
                  onChange={setShowFullData}
                  checkedChildren="Full Data"
                  unCheckedChildren="Filtered"
                  className="mr-2"
                  size="small"
                />
              </Tooltip>
            </div>
          </div>
          <Tabs defaultActiveKey="0" style={{ height: 'calc(100% - 40px)' }} onChange={handleTabChange} className='[&_.ant-tabs-nav]:m-0'>
          {columnTabs.map((tab, index) => (
            <Tabs.TabPane tab={tab.name} key={index.toString()}>
              <div className="h-full w-full">
                {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
                  <ReactECharts
                    key={`tab-${index}-${chartUpdateTrigger}`}
                    ref={echartsRef}
                    option={createEChartsOption(tab.data, tab.name, tab.yRange as [number, number] | undefined)}
                    style={{ width: '100%', height: '100%', minHeight: '300px' }}
                    onEvents={{
                      brushEnd: handleEChartsEvents.brushEnd,
                      dataZoom: handleEChartsEvents.dataZoom
                    }}
                    opts={{ renderer: 'canvas' }}
                  />
                ) : (
                  <div style={{ width: '100%', height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Spin size="large" tip="Loading chart..." />
                  </div>
                )}
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs></>)
        }

        {/* Selection Popup */}
        {selectionPopup.visible && (
          <div
            className="selection-popup"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)', // Perfect centering
              zIndex: 100, // Lower z-index to not overlap other panels
              backgroundColor: 'white',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
              padding: '8px 0',
              minWidth: '220px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}
          >
            <div style={{
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#262626',
              borderBottom: '1px solid #f0f0f0',
              marginBottom: '4px'
            }}>
              Selection Actions
            </div>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div
                onClick={() => handlePopupAction('zoom')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>🔍</span>
                Zoom to Selection
              </div>
              <div
                onClick={() => handlePopupAction('anomaly')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>📊</span>
                Mark as Anomaly
              </div>
              <div
                onClick={() => handlePopupAction('operation')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>⚙️</span>
                Set Operation Range
              </div>
              <div style={{ height: '1px', backgroundColor: '#f0f0f0', margin: '4px 0' }} />
              <div
                onClick={() => setSelectionPopup(prev => ({ ...prev, visible: false }))}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#8c8c8c',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>✕</span>
                Cancel
              </div>
            </div>
          </div>
        )}
      </div>
    );
  // } else {
  //   // Original tab-based view
  //   return (
  //     <div className="time-series-panel h-full">
  //       <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
  //         {columnTabs.map((tab, index) => (
  //           <Tabs.TabPane tab={tab.name} key={index.toString()}>
  //             <div className="p-4 h-full">
  //               <Plot
  //                 data={tab.data as any}
  //                 layout={{
  //                   ...layout,
  //                   title: `Time Series: ${tab.name}`,
  //                   showlegend: tab.data.length > 1
  //                 } as any}
  //                 config={config as any}
  //                 style={{ width: '100%', height: 'calc(100% - 20px)' }}
  //                 useResizeHandler={true}
  //               />
  //             </div>
  //           </Tabs.TabPane>
  //         ))}
  //       </Tabs>
  //     </div>
  //   );
  // }
};

export default TimeSeriesPanel;
